'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowUpIcon, StarIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import { SubscriptionTier, TIER_CONFIGS } from '@/lib/stripe';
import { useSubscription } from '@/hooks/useSubscription';
import { useRouter } from 'next/navigation';

interface UpgradePromptProps {
  feature: 'custom_roles' | 'knowledge_base' | 'advanced_routing' | 'prompt_engineering' | 'semantic_caching';
  currentTier: SubscriptionTier;
  customMessage?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'card' | 'banner' | 'inline';
}

const featureDisplayNames = {
  custom_roles: 'Custom Roles',
  knowledge_base: 'Knowledge Base',
  advanced_routing: 'Advanced Routing',
  prompt_engineering: 'Prompt Engineering',
  semantic_caching: 'Semantic Caching'
};

const getMinimumTierForFeature = (feature: string): SubscriptionTier => {
  const tiers: SubscriptionTier[] = ['starter', 'professional', 'enterprise'];
  
  for (const tier of tiers) {
    const config = TIER_CONFIGS[tier];
    switch (feature) {
      case 'custom_roles':
        if (config.limits.canUseCustomRoles) return tier;
        break;
      case 'knowledge_base':
        if (config.limits.canUseKnowledgeBase) return tier;
        break;
      case 'advanced_routing':
        if (config.limits.canUseAdvancedRouting) return tier;
        break;
      case 'prompt_engineering':
        if (config.limits.canUsePromptEngineering) return tier;
        break;
      case 'semantic_caching':
        if (config.limits.canUseSemanticCaching) return tier;
        break;
    }
  }
  return 'starter';
};

export function UpgradePrompt({ 
  feature, 
  currentTier, 
  customMessage, 
  size = 'md',
  variant = 'card' 
}: UpgradePromptProps) {
  const { createCheckoutSession } = useSubscription();
  const router = useRouter();
  const minimumTier = getMinimumTierForFeature(feature);
  const minimumTierConfig = TIER_CONFIGS[minimumTier];
  const featureName = featureDisplayNames[feature];

  const handleUpgrade = async () => {
    try {
      if (minimumTier === 'starter') {
        await createCheckoutSession('starter');
      } else if (minimumTier === 'professional') {
        await createCheckoutSession('professional');
      } else {
        router.push('/pricing');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      router.push('/pricing');
    }
  };

  const sizeClasses = {
    sm: 'p-4 text-sm',
    md: 'p-6 text-base',
    lg: 'p-8 text-lg'
  };

  const variantClasses = {
    card: 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm',
    banner: 'bg-orange-100 border-l-4 border-orange-500 rounded-r-lg',
    inline: 'bg-orange-50 border border-orange-200 rounded-lg'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${variantClasses[variant]} ${sizeClasses[size]}`}
    >
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
            <LockClosedIcon className="w-5 h-5 text-white" />
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {featureName} - Premium Feature
          </h3>
          
          <p className="text-gray-700 mb-4">
            {customMessage || 
              `${featureName} is available starting with the ${minimumTierConfig.name} plan. 
               Upgrade to unlock this powerful feature and enhance your RouKey experience.`
            }
          </p>

          <div className="flex items-center space-x-4">
            <button
              onClick={handleUpgrade}
              className="inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200"
            >
              <ArrowUpIcon className="w-4 h-4 mr-2" />
              Upgrade to {minimumTierConfig.name}
            </button>
            
            <button
              onClick={() => router.push('/pricing')}
              className="inline-flex items-center px-4 py-2 text-orange-600 text-sm font-medium hover:text-orange-700 transition-colors duration-200"
            >
              <StarIcon className="w-4 h-4 mr-2" />
              View All Plans
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

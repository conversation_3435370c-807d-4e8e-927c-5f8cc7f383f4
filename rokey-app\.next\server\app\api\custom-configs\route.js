/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/custom-configs/route";
exports.ids = ["app/api/custom-configs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_custom_configs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/custom-configs/route.ts */ \"(rsc)/./src/app/api/custom-configs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/custom-configs/route\",\n        pathname: \"/api/custom-configs\",\n        filename: \"route\",\n        bundlePath: \"app/api/custom-configs/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\custom-configs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_custom_configs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/custom-configs/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/custom-configs/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n\n\n\n// POST /api/custom-configs\n// Creates a new custom API configuration\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in POST /api/custom-configs:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create configurations.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const { name } = await request.json();\n        if (!name || typeof name !== 'string' || name.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration name is required and must be a non-empty string'\n            }, {\n                status: 400\n            });\n        }\n        if (name.length > 255) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration name must be 255 characters or less'\n            }, {\n                status: 400\n            });\n        }\n        // Check user's subscription tier and configuration limits\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Count current configurations for this user\n        const { count: currentConfigCount } = await supabase.from('custom_api_configs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', user.id);\n        // Check if user can create more configurations\n        if (!(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.canPerformAction)(userTier, 'create_config', currentConfigCount || 0)) {\n            const tierConfig = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.getTierConfig)(userTier);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `You have reached the maximum number of configurations (${tierConfig.limits.configurations}) for your ${userTier} plan. Please upgrade to create more configurations.`\n            }, {\n                status: 403\n            });\n        }\n        const { data, error } = await supabase.from('custom_api_configs').insert([\n            {\n                name,\n                user_id: user.id\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Supabase error creating custom config:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create custom API configuration',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/custom-configs:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/custom-configs\n// Lists all custom API configurations (for the authenticated user in M13)\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in GET /api/custom-configs:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view configurations.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        // Phase 2A Optimization: Optimized query with only needed fields and caching\n        const { data, error } = await supabase.from('custom_api_configs').select('id, name, created_at, updated_at, routing_strategy') // Only select needed fields\n        .eq('user_id', user.id) // Filter by authenticated user's ID\n        .order('created_at', {\n            ascending: false\n        }).limit(100); // Reasonable limit to prevent large responses\n        if (error) {\n            console.error('Supabase error fetching custom configs:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch custom API configurations',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data || [], {\n            status: 200\n        });\n        // Phase 2A Optimization: Add aggressive caching headers\n        const isPrefetch = request.headers.get('X-Prefetch') === 'true';\n        const cacheMaxAge = isPrefetch ? 600 : 120; // 10 minutes for prefetch, 2 minutes for regular\n        response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=300`);\n        response.headers.set('X-Content-Type-Options', 'nosniff');\n        response.headers.set('Vary', 'X-Prefetch');\n        return response;\n    } catch (e) {\n        console.error('Error in GET /api/custom-configs:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/custom-configs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$19',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '4 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback + Complex routing (1 config limit)',\n            'Up to 3 custom roles',\n            'Intelligent role routing (1 config)',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 4,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$49',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            '20 Custom Configurations',\n            '15 API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 20,\n            apiKeysPerConfig: 15,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.ENTERPRISE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS1jb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSxrREFBa0Q7QUFDbEQseUVBQXlFO0FBRXpFLE1BQU1BLGVBQWVDLGtCQUF5QjtBQUU5QyxtREFBbUQ7QUFDNUMsTUFBTUMsY0FBYztJQUN6QkMsZ0JBQWdCSCxlQUNaQyxRQUFRRyxHQUFHLENBQUNDLDJCQUEyQixHQUN2Q0osUUFBUUcsR0FBRyxDQUFDRSwyQkFBMkI7SUFFM0NDLFdBQVdQLGVBQ1BDLFFBQVFHLEdBQUcsQ0FBQ0ksc0JBQXNCLEdBQ2xDUCxRQUFRRyxHQUFHLENBQUNLLHNCQUFzQjtJQUV0Q0MsZUFBZVQsUUFBUUcsR0FBRyxDQUFDTyxxQkFBcUI7QUFDbEQsRUFBRTtBQUVGLHdEQUF3RDtBQUNqRCxNQUFNQyxtQkFBbUI7SUFDOUJDLE1BQU1iLGVBQ0ZDLFFBQVFHLEdBQUcsQ0FBQ1UseUJBQXlCLEdBQ3JDYixRQUFRRyxHQUFHLENBQUNXLHlCQUF5QjtJQUV6Q0MsU0FBU2hCLGVBQ0xDLFFBQVFHLEdBQUcsQ0FBQ2EsNEJBQTRCLEdBQ3hDaEIsUUFBUUcsR0FBRyxDQUFDYyw0QkFBNEI7SUFFNUNDLGNBQWNuQixlQUNWQyxRQUFRRyxHQUFHLENBQUNnQixpQ0FBaUMsR0FDN0NuQixRQUFRRyxHQUFHLENBQUNpQixpQ0FBaUM7SUFFakRDLFlBQVl0QixlQUNSQyxRQUFRRyxHQUFHLENBQUNtQiwrQkFBK0IsR0FDM0N0QixRQUFRRyxHQUFHLENBQUNvQiwrQkFBK0I7QUFDakQsRUFBRTtBQUVGLDBEQUEwRDtBQUNuRCxNQUFNQyxxQkFBcUI7SUFDaENaLE1BQU1iLGVBQ0ZDLFFBQVFHLEdBQUcsQ0FBQ3NCLDJCQUEyQixHQUN2Q3pCLFFBQVFHLEdBQUcsQ0FBQ3VCLDJCQUEyQjtJQUUzQ1gsU0FBU2hCLGVBQ0xDLFFBQVFHLEdBQUcsQ0FBQ3dCLDhCQUE4QixHQUMxQzNCLFFBQVFHLEdBQUcsQ0FBQ3lCLDhCQUE4QjtJQUU5Q1YsY0FBY25CLGVBQ1ZDLFFBQVFHLEdBQUcsQ0FBQzBCLG1DQUFtQyxHQUMvQzdCLFFBQVFHLEdBQUcsQ0FBQzJCLG1DQUFtQztJQUVuRFQsWUFBWXRCLGVBQ1JDLFFBQVFHLEdBQUcsQ0FBQzRCLGlDQUFpQyxHQUM3Qy9CLFFBQVFHLEdBQUcsQ0FBQzZCLGlDQUFpQztBQUNuRCxFQUFFO0FBRUYscUVBQXFFO0FBQzlELE1BQU1DLDBCQUEwQjtJQUNyQ3JCLE1BQU1iLGVBQ0ZDLFFBQVFHLEdBQUcsQ0FBQ1UseUJBQXlCLEdBQ3JDYixRQUFRRyxHQUFHLENBQUNXLHlCQUF5QjtJQUV6Q0MsU0FBU2hCLGVBQ0xDLFFBQVFHLEdBQUcsQ0FBQ2EsNEJBQTRCLEdBQ3hDaEIsUUFBUUcsR0FBRyxDQUFDYyw0QkFBNEI7SUFFNUNDLGNBQWNuQixlQUNWQyxRQUFRRyxHQUFHLENBQUNnQixpQ0FBaUMsR0FDN0NuQixRQUFRRyxHQUFHLENBQUNpQixpQ0FBaUM7SUFFakRDLFlBQVl0QixlQUNSQyxRQUFRRyxHQUFHLENBQUNtQiwrQkFBK0IsR0FDM0N0QixRQUFRRyxHQUFHLENBQUNvQiwrQkFBK0I7QUFDakQsRUFBRTtBQUVGLGlDQUFpQztBQUMxQixNQUFNVyxrQkFBa0I7SUFDN0JuQztJQUNBb0MsYUFBYXBDLGVBQWUsU0FBUztJQUNyQ3FDLFVBQVU7UUFDUkMsYUFBYXBDLFlBQVlDLGNBQWMsR0FBR0QsWUFBWUMsY0FBYyxDQUFDb0MsU0FBUyxDQUFDLEdBQUcsTUFBTSxRQUFRO1FBQ2hHQyxRQUFRdEMsWUFBWUssU0FBUyxHQUFHTCxZQUFZSyxTQUFTLENBQUNnQyxTQUFTLENBQUMsR0FBRyxNQUFNLFFBQVE7SUFDbkY7QUFDRixFQUFFO0FBRUYsc0NBQXNDO0FBQ3RDLElBQUksQ0FBQ3ZDLGNBQWM7SUFDakJ5QyxRQUFRQyxHQUFHLENBQUMsMEJBQTBCUCxnQkFBZ0JDLFdBQVc7SUFDakVLLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JQLGdCQUFnQkUsUUFBUTtBQUN4RCIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGxpYlxcc3RyaXBlLWNvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTdHJpcGUgQ29uZmlndXJhdGlvbiB3aXRoIEVudmlyb25tZW50IERldGVjdGlvblxuLy8gQXV0b21hdGljYWxseSBzd2l0Y2hlcyBiZXR3ZWVuIHRlc3QgYW5kIGxpdmUga2V5cyBiYXNlZCBvbiBlbnZpcm9ubWVudFxuXG5jb25zdCBpc1Byb2R1Y3Rpb24gPSBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nO1xuXG4vLyBTdHJpcGUgS2V5cyAtIEF1dG8tc2VsZWN0ZWQgYmFzZWQgb24gZW52aXJvbm1lbnRcbmV4cG9ydCBjb25zdCBTVFJJUEVfS0VZUyA9IHtcbiAgcHVibGlzaGFibGVLZXk6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX1BVQkxJU0hBQkxFX0tFWSFcbiAgICA6IHByb2Nlc3MuZW52LlNUUklQRV9URVNUX1BVQkxJU0hBQkxFX0tFWSEsXG4gIFxuICBzZWNyZXRLZXk6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX1NFQ1JFVF9LRVkhXG4gICAgOiBwcm9jZXNzLmVudi5TVFJJUEVfVEVTVF9TRUNSRVRfS0VZISxcbiAgICBcbiAgd2ViaG9va1NlY3JldDogcHJvY2Vzcy5lbnYuU1RSSVBFX1dFQkhPT0tfU0VDUkVUIVxufTtcblxuLy8gU3RyaXBlIFByaWNlIElEcyAtIEF1dG8tc2VsZWN0ZWQgYmFzZWQgb24gZW52aXJvbm1lbnRcbmV4cG9ydCBjb25zdCBTVFJJUEVfUFJJQ0VfSURTID0ge1xuICBGUkVFOiBpc1Byb2R1Y3Rpb24gXG4gICAgPyBwcm9jZXNzLmVudi5TVFJJUEVfTElWRV9GUkVFX1BSSUNFX0lEIVxuICAgIDogcHJvY2Vzcy5lbnYuU1RSSVBFX1RFU1RfRlJFRV9QUklDRV9JRCEsXG4gICAgXG4gIFNUQVJURVI6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX1NUQVJURVJfUFJJQ0VfSUQhXG4gICAgOiBwcm9jZXNzLmVudi5TVFJJUEVfVEVTVF9TVEFSVEVSX1BSSUNFX0lEISxcbiAgICBcbiAgUFJPRkVTU0lPTkFMOiBpc1Byb2R1Y3Rpb24gXG4gICAgPyBwcm9jZXNzLmVudi5TVFJJUEVfTElWRV9QUk9GRVNTSU9OQUxfUFJJQ0VfSUQhXG4gICAgOiBwcm9jZXNzLmVudi5TVFJJUEVfVEVTVF9QUk9GRVNTSU9OQUxfUFJJQ0VfSUQhLFxuICAgIFxuICBFTlRFUlBSSVNFOiBpc1Byb2R1Y3Rpb24gXG4gICAgPyBwcm9jZXNzLmVudi5TVFJJUEVfTElWRV9FTlRFUlBSSVNFX1BSSUNFX0lEIVxuICAgIDogcHJvY2Vzcy5lbnYuU1RSSVBFX1RFU1RfRU5URVJQUklTRV9QUklDRV9JRCFcbn07XG5cbi8vIFN0cmlwZSBQcm9kdWN0IElEcyAtIEF1dG8tc2VsZWN0ZWQgYmFzZWQgb24gZW52aXJvbm1lbnRcbmV4cG9ydCBjb25zdCBTVFJJUEVfUFJPRFVDVF9JRFMgPSB7XG4gIEZSRUU6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX0ZSRUVfUFJPRFVDVF9JRCFcbiAgICA6IHByb2Nlc3MuZW52LlNUUklQRV9URVNUX0ZSRUVfUFJPRFVDVF9JRCEsXG4gICAgXG4gIFNUQVJURVI6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX1NUQVJURVJfUFJPRFVDVF9JRCFcbiAgICA6IHByb2Nlc3MuZW52LlNUUklQRV9URVNUX1NUQVJURVJfUFJPRFVDVF9JRCEsXG4gICAgXG4gIFBST0ZFU1NJT05BTDogaXNQcm9kdWN0aW9uIFxuICAgID8gcHJvY2Vzcy5lbnYuU1RSSVBFX0xJVkVfUFJPRkVTU0lPTkFMX1BST0RVQ1RfSUQhXG4gICAgOiBwcm9jZXNzLmVudi5TVFJJUEVfVEVTVF9QUk9GRVNTSU9OQUxfUFJPRFVDVF9JRCEsXG4gICAgXG4gIEVOVEVSUFJJU0U6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX0VOVEVSUFJJU0VfUFJPRFVDVF9JRCFcbiAgICA6IHByb2Nlc3MuZW52LlNUUklQRV9URVNUX0VOVEVSUFJJU0VfUFJPRFVDVF9JRCFcbn07XG5cbi8vIFB1YmxpYyBQcmljZSBJRHMgZm9yIGZyb250ZW5kIChhdXRvLXNlbGVjdGVkIGJhc2VkIG9uIGVudmlyb25tZW50KVxuZXhwb3J0IGNvbnN0IFBVQkxJQ19TVFJJUEVfUFJJQ0VfSURTID0ge1xuICBGUkVFOiBpc1Byb2R1Y3Rpb24gXG4gICAgPyBwcm9jZXNzLmVudi5TVFJJUEVfTElWRV9GUkVFX1BSSUNFX0lEIVxuICAgIDogcHJvY2Vzcy5lbnYuU1RSSVBFX1RFU1RfRlJFRV9QUklDRV9JRCEsXG4gICAgXG4gIFNUQVJURVI6IGlzUHJvZHVjdGlvbiBcbiAgICA/IHByb2Nlc3MuZW52LlNUUklQRV9MSVZFX1NUQVJURVJfUFJJQ0VfSUQhXG4gICAgOiBwcm9jZXNzLmVudi5TVFJJUEVfVEVTVF9TVEFSVEVSX1BSSUNFX0lEISxcbiAgICBcbiAgUFJPRkVTU0lPTkFMOiBpc1Byb2R1Y3Rpb24gXG4gICAgPyBwcm9jZXNzLmVudi5TVFJJUEVfTElWRV9QUk9GRVNTSU9OQUxfUFJJQ0VfSUQhXG4gICAgOiBwcm9jZXNzLmVudi5TVFJJUEVfVEVTVF9QUk9GRVNTSU9OQUxfUFJJQ0VfSUQhLFxuICAgIFxuICBFTlRFUlBSSVNFOiBpc1Byb2R1Y3Rpb24gXG4gICAgPyBwcm9jZXNzLmVudi5TVFJJUEVfTElWRV9FTlRFUlBSSVNFX1BSSUNFX0lEIVxuICAgIDogcHJvY2Vzcy5lbnYuU1RSSVBFX1RFU1RfRU5URVJQUklTRV9QUklDRV9JRCFcbn07XG5cbi8vIEVudmlyb25tZW50IGluZm8gZm9yIGRlYnVnZ2luZ1xuZXhwb3J0IGNvbnN0IFNUUklQRV9FTlZfSU5GTyA9IHtcbiAgaXNQcm9kdWN0aW9uLFxuICBlbnZpcm9ubWVudDogaXNQcm9kdWN0aW9uID8gJ0xJVkUnIDogJ1RFU1QnLFxuICBrZXlzVXNlZDoge1xuICAgIHB1Ymxpc2hhYmxlOiBTVFJJUEVfS0VZUy5wdWJsaXNoYWJsZUtleSA/IFNUUklQRV9LRVlTLnB1Ymxpc2hhYmxlS2V5LnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyA6ICd1bmRlZmluZWQnLFxuICAgIHNlY3JldDogU1RSSVBFX0tFWVMuc2VjcmV0S2V5ID8gU1RSSVBFX0tFWVMuc2VjcmV0S2V5LnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyA6ICd1bmRlZmluZWQnXG4gIH1cbn07XG5cbi8vIExvZyBlbnZpcm9ubWVudCBpbmZvIGluIGRldmVsb3BtZW50XG5pZiAoIWlzUHJvZHVjdGlvbikge1xuICBjb25zb2xlLmxvZygn8J+UpyBTdHJpcGUgRW52aXJvbm1lbnQ6JywgU1RSSVBFX0VOVl9JTkZPLmVudmlyb25tZW50KTtcbiAgY29uc29sZS5sb2coJ/CflJEgVXNpbmcga2V5czonLCBTVFJJUEVfRU5WX0lORk8ua2V5c1VzZWQpO1xufVxuIl0sIm5hbWVzIjpbImlzUHJvZHVjdGlvbiIsInByb2Nlc3MiLCJTVFJJUEVfS0VZUyIsInB1Ymxpc2hhYmxlS2V5IiwiZW52IiwiU1RSSVBFX0xJVkVfUFVCTElTSEFCTEVfS0VZIiwiU1RSSVBFX1RFU1RfUFVCTElTSEFCTEVfS0VZIiwic2VjcmV0S2V5IiwiU1RSSVBFX0xJVkVfU0VDUkVUX0tFWSIsIlNUUklQRV9URVNUX1NFQ1JFVF9LRVkiLCJ3ZWJob29rU2VjcmV0IiwiU1RSSVBFX1dFQkhPT0tfU0VDUkVUIiwiU1RSSVBFX1BSSUNFX0lEUyIsIkZSRUUiLCJTVFJJUEVfTElWRV9GUkVFX1BSSUNFX0lEIiwiU1RSSVBFX1RFU1RfRlJFRV9QUklDRV9JRCIsIlNUQVJURVIiLCJTVFJJUEVfTElWRV9TVEFSVEVSX1BSSUNFX0lEIiwiU1RSSVBFX1RFU1RfU1RBUlRFUl9QUklDRV9JRCIsIlBST0ZFU1NJT05BTCIsIlNUUklQRV9MSVZFX1BST0ZFU1NJT05BTF9QUklDRV9JRCIsIlNUUklQRV9URVNUX1BST0ZFU1NJT05BTF9QUklDRV9JRCIsIkVOVEVSUFJJU0UiLCJTVFJJUEVfTElWRV9FTlRFUlBSSVNFX1BSSUNFX0lEIiwiU1RSSVBFX1RFU1RfRU5URVJQUklTRV9QUklDRV9JRCIsIlNUUklQRV9QUk9EVUNUX0lEUyIsIlNUUklQRV9MSVZFX0ZSRUVfUFJPRFVDVF9JRCIsIlNUUklQRV9URVNUX0ZSRUVfUFJPRFVDVF9JRCIsIlNUUklQRV9MSVZFX1NUQVJURVJfUFJPRFVDVF9JRCIsIlNUUklQRV9URVNUX1NUQVJURVJfUFJPRFVDVF9JRCIsIlNUUklQRV9MSVZFX1BST0ZFU1NJT05BTF9QUk9EVUNUX0lEIiwiU1RSSVBFX1RFU1RfUFJPRkVTU0lPTkFMX1BST0RVQ1RfSUQiLCJTVFJJUEVfTElWRV9FTlRFUlBSSVNFX1BST0RVQ1RfSUQiLCJTVFJJUEVfVEVTVF9FTlRFUlBSSVNFX1BST0RVQ1RfSUQiLCJQVUJMSUNfU1RSSVBFX1BSSUNFX0lEUyIsIlNUUklQRV9FTlZfSU5GTyIsImVudmlyb25tZW50Iiwia2V5c1VzZWQiLCJwdWJsaXNoYWJsZSIsInN1YnN0cmluZyIsInNlY3JldCIsImNvbnNvbGUiLCJsb2ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/custom-configs/route";
exports.ids = ["app/api/custom-configs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_custom_configs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/custom-configs/route.ts */ \"(rsc)/./src/app/api/custom-configs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/custom-configs/route\",\n        pathname: \"/api/custom-configs\",\n        filename: \"route\",\n        bundlePath: \"app/api/custom-configs/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\custom-configs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_custom_configs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjdXN0b20tY29uZmlncyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGY3VzdG9tLWNvbmZpZ3MlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZjdXN0b20tY29uZmlncyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUm9LZXklMjBBcHAlNUNyb2tleS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNSb0tleSUyMEFwcCU1Q3Jva2V5LWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDaUI7QUFDOUY7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY3VzdG9tLWNvbmZpZ3NcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2N1c3RvbS1jb25maWdzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY3VzdG9tLWNvbmZpZ3NcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2N1c3RvbS1jb25maWdzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcUm9LZXkgQXBwXFxcXHJva2V5LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxjdXN0b20tY29uZmlnc1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/custom-configs/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/custom-configs/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe */ \"(rsc)/./src/lib/stripe.ts\");\n\n\n\n// POST /api/custom-configs\n// Creates a new custom API configuration\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in POST /api/custom-configs:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create configurations.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const { name } = await request.json();\n        if (!name || typeof name !== 'string' || name.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration name is required and must be a non-empty string'\n            }, {\n                status: 400\n            });\n        }\n        if (name.length > 255) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration name must be 255 characters or less'\n            }, {\n                status: 400\n            });\n        }\n        // Check user's subscription tier and configuration limits\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Count current configurations for this user\n        const { count: currentConfigCount } = await supabase.from('custom_api_configs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', user.id);\n        // Check if user can create more configurations\n        if (!(0,_lib_stripe__WEBPACK_IMPORTED_MODULE_2__.canPerformAction)(userTier, 'create_config', currentConfigCount || 0)) {\n            const tierConfig = (0,_lib_stripe__WEBPACK_IMPORTED_MODULE_2__.getTierConfig)(userTier);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `You have reached the maximum number of configurations (${tierConfig.limits.configurations}) for your ${userTier} plan. Please upgrade to create more configurations.`\n            }, {\n                status: 403\n            });\n        }\n        const { data, error } = await supabase.from('custom_api_configs').insert([\n            {\n                name,\n                user_id: user.id\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Supabase error creating custom config:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create custom API configuration',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/custom-configs:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/custom-configs\n// Lists all custom API configurations (for the authenticated user in M13)\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in GET /api/custom-configs:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view configurations.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        // Phase 2A Optimization: Optimized query with only needed fields and caching\n        const { data, error } = await supabase.from('custom_api_configs').select('id, name, created_at, updated_at, routing_strategy') // Only select needed fields\n        .eq('user_id', user.id) // Filter by authenticated user's ID\n        .order('created_at', {\n            ascending: false\n        }).limit(100); // Reasonable limit to prevent large responses\n        if (error) {\n            console.error('Supabase error fetching custom configs:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch custom API configurations',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data || [], {\n            status: 200\n        });\n        // Phase 2A Optimization: Add aggressive caching headers\n        const isPrefetch = request.headers.get('X-Prefetch') === 'true';\n        const cacheMaxAge = isPrefetch ? 600 : 120; // 10 minutes for prefetch, 2 minutes for regular\n        response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=300`);\n        response.headers.set('X-Content-Type-Options', 'nosniff');\n        response.headers.set('Vary', 'X-Prefetch');\n        return response;\n    } catch (e) {\n        console.error('Error in GET /api/custom-configs:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/custom-configs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey.substring(0, 20) + '...',\n        secret: STRIPE_KEYS.secretKey.substring(0, 20) + '...'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STRIPE_CONFIG: () => (/* binding */ STRIPE_CONFIG),\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](_stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_KEYS.secretKey, {\n    apiVersion: '2025-02-24.acacia'\n});\nconst STRIPE_CONFIG = {\n    PRICE_IDS: _stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_PRICE_IDS,\n    PRODUCT_IDS: _stripe_config__WEBPACK_IMPORTED_MODULE_1__.STRIPE_PRODUCT_IDS\n};\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: STRIPE_CONFIG.PRICE_IDS.FREE,\n        productId: STRIPE_CONFIG.PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$19',\n        priceId: STRIPE_CONFIG.PRICE_IDS.STARTER,\n        productId: STRIPE_CONFIG.PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '4 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback + Complex routing (1 config limit)',\n            'Up to 3 custom roles',\n            'Intelligent role routing (1 config)',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 4,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$49',\n        priceId: STRIPE_CONFIG.PRICE_IDS.PROFESSIONAL,\n        productId: STRIPE_CONFIG.PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            '20 Custom Configurations',\n            '15 API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 20,\n            apiKeysPerConfig: 15,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: STRIPE_CONFIG.PRICE_IDS.ENTERPRISE,\n        productId: STRIPE_CONFIG.PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVFO0FBQ2hDO0FBR3ZDLG1FQUFtRTtBQUNuRSw2RUFBNkU7QUFDN0UsbURBQW1EO0FBQzVDLGVBQWVFO0lBQ3BCLE1BQU1DLGNBQWMsTUFBTUYscURBQU9BO0lBRWpDLE9BQU9ELGlFQUFrQkEsQ0FDdkJJLDBDQUFvQyxFQUNwQ0Esa05BQXlDLEVBQ3pDO1FBQ0VILFNBQVM7WUFDUE8sS0FBSUMsSUFBWTtnQkFDZCxPQUFPTixZQUFZSyxHQUFHLENBQUNDLE9BQU9DO1lBQ2hDO1lBQ0FDLEtBQUlGLElBQVksRUFBRUMsS0FBYSxFQUFFRSxPQUFzQjtnQkFDckQsSUFBSTtvQkFDRlQsWUFBWVEsR0FBRyxDQUFDO3dCQUFFRjt3QkFBTUM7d0JBQU8sR0FBR0UsT0FBTztvQkFBQztnQkFDNUMsRUFBRSxPQUFPQyxPQUFPO29CQUNkLDZEQUE2RDtvQkFDN0QsZ0VBQWdFO29CQUNoRSwrQ0FBK0M7b0JBQy9DQyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxzQkFBc0IsRUFBRU4sS0FBSyxtQ0FBbUMsQ0FBQyxFQUFFSTtnQkFDbkY7WUFDRjtZQUNBRyxRQUFPUCxJQUFZLEVBQUVHLE9BQXNCO2dCQUN6QyxJQUFJO29CQUNGLGlFQUFpRTtvQkFDakUsd0ZBQXdGO29CQUN4RlQsWUFBWVEsR0FBRyxDQUFDO3dCQUFFRjt3QkFBTUMsT0FBTzt3QkFBSSxHQUFHRSxPQUFPO29CQUFDO2dCQUNoRCxFQUFFLE9BQU9DLE9BQU87b0JBQ2QseURBQXlEO29CQUN6REMsUUFBUUMsSUFBSSxDQUFDLENBQUMseUJBQXlCLEVBQUVOLEtBQUssbUNBQW1DLENBQUMsRUFBRUk7Z0JBQ3RGO1lBQ0Y7UUFDRjtJQUNGO0FBRUo7QUFFQSw2RUFBNkU7QUFDdEUsU0FBU0ksc0NBQXNDQyxPQUFvQjtJQUN4RSxPQUFPbEIsaUVBQWtCQSxDQUN2QkksMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUgsU0FBUztZQUNQTyxLQUFJQyxJQUFZO2dCQUNkLE9BQU9TLFFBQVFqQixPQUFPLENBQUNPLEdBQUcsQ0FBQ0MsT0FBT0M7WUFDcEM7WUFDQUMsS0FBSUYsSUFBWSxFQUFFQyxLQUFhLEVBQUVFLE9BQXNCO1lBQ3JELDhEQUE4RDtZQUM5RCx1Q0FBdUM7WUFDekM7WUFDQUksUUFBT1AsSUFBWSxFQUFFRyxPQUFzQjtZQUN6QyxpRUFBaUU7WUFDakUsdUNBQXVDO1lBQ3pDO1FBQ0Y7SUFDRjtBQUVKIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcbGliXFxzdXBhYmFzZVxcc2VydmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNlcnZlckNsaWVudCwgdHlwZSBDb29raWVPcHRpb25zIH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XHJcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tICduZXh0L2hlYWRlcnMnO1xyXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcclxuXHJcbi8vIFRoaXMgaXMgdGhlIHN0YW5kYXJkIHNldHVwIGZvciBjcmVhdGluZyBhIFN1cGFiYXNlIHNlcnZlciBjbGllbnRcclxuLy8gaW4gTmV4dC5qcyBBcHAgUm91dGVyIChTZXJ2ZXIgQ29tcG9uZW50cywgUm91dGUgSGFuZGxlcnMsIFNlcnZlciBBY3Rpb25zKS5cclxuLy8gVXBkYXRlZCBmb3IgTmV4dC5qcyAxNSBhc3luYyBjb29raWVzIHJlcXVpcmVtZW50XHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudE9uUmVxdWVzdCgpIHtcclxuICBjb25zdCBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKTtcclxuXHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXHJcbiAgICB7XHJcbiAgICAgIGNvb2tpZXM6IHtcclxuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgICByZXR1cm4gY29va2llU3RvcmUuZ2V0KG5hbWUpPy52YWx1ZTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIHNldChuYW1lOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcsIG9wdGlvbnM6IENvb2tpZU9wdGlvbnMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldCh7IG5hbWUsIHZhbHVlLCAuLi5vcHRpb25zIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gVGhpcyBlcnJvciBjYW4gYmUgaWdub3JlZCBpZiBydW5uaW5nIGluIGEgU2VydmVyIENvbXBvbmVudFxyXG4gICAgICAgICAgICAvLyB3aGVyZSBjb29raWVzIGNhbid0IGJlIHNldCBkaXJlY3RseS4gQ29va2llIHNldHRpbmcgc2hvdWxkIGJlXHJcbiAgICAgICAgICAgIC8vIGhhbmRsZWQgaW4gU2VydmVyIEFjdGlvbnMgb3IgUm91dGUgSGFuZGxlcnMuXHJcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHNldCBjb29raWUgJyR7bmFtZX0nIChtaWdodCBiZSBpbiBhIFNlcnZlciBDb21wb25lbnQpOmAsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIHJlbW92ZShuYW1lOiBzdHJpbmcsIG9wdGlvbnM6IENvb2tpZU9wdGlvbnMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vIFRvIHJlbW92ZSBhIGNvb2tpZSB1c2luZyB0aGUgYHNldGAgbWV0aG9kIGZyb20gYG5leHQvaGVhZGVyc2AsXHJcbiAgICAgICAgICAgIC8vIHlvdSB0eXBpY2FsbHkgc2V0IGl0IHdpdGggYW4gZW1wdHkgdmFsdWUgYW5kIE1heC1BZ2U9MCBvciBhbiBleHBpcnkgZGF0ZSBpbiB0aGUgcGFzdC5cclxuICAgICAgICAgICAgY29va2llU3RvcmUuc2V0KHsgbmFtZSwgdmFsdWU6ICcnLCAuLi5vcHRpb25zIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gU2ltaWxhciB0byBzZXQsIHRoaXMgbWlnaHQgZmFpbCBpbiBhIFNlcnZlciBDb21wb25lbnQuXHJcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHJlbW92ZSBjb29raWUgJyR7bmFtZX0nIChtaWdodCBiZSBpbiBhIFNlcnZlciBDb21wb25lbnQpOmAsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfVxyXG4gICk7XHJcbn1cclxuXHJcbi8vIEFsdGVybmF0aXZlIG1ldGhvZCBmb3IgQVBJIHJvdXRlcyB0aGF0IG5lZWQgdG8gaGFuZGxlIGNvb2tpZXMgZnJvbSByZXF1ZXN0XHJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudEZyb21SZXF1ZXN0KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXHJcbiAgICB7XHJcbiAgICAgIGNvb2tpZXM6IHtcclxuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgICByZXR1cm4gcmVxdWVzdC5jb29raWVzLmdldChuYW1lKT8udmFsdWU7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBzZXQobmFtZTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nLCBvcHRpb25zOiBDb29raWVPcHRpb25zKSB7XHJcbiAgICAgICAgICAvLyBJbiBBUEkgcm91dGVzLCB3ZSBjYW4ndCBzZXQgY29va2llcyBkaXJlY3RseSBvbiB0aGUgcmVxdWVzdFxyXG4gICAgICAgICAgLy8gVGhpcyB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgfSxcclxuICAgICAgICByZW1vdmUobmFtZTogc3RyaW5nLCBvcHRpb25zOiBDb29raWVPcHRpb25zKSB7XHJcbiAgICAgICAgICAvLyBJbiBBUEkgcm91dGVzLCB3ZSBjYW4ndCByZW1vdmUgY29va2llcyBkaXJlY3RseSBvbiB0aGUgcmVxdWVzdFxyXG4gICAgICAgICAgLy8gVGhpcyB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH1cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRPblJlcXVlc3QiLCJjb29raWVTdG9yZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImdldCIsIm5hbWUiLCJ2YWx1ZSIsInNldCIsIm9wdGlvbnMiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwicmVtb3ZlIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRGcm9tUmVxdWVzdCIsInJlcXVlc3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2Froute&page=%2Fapi%2Fcustom-configs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
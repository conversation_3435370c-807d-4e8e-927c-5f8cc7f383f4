"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/TierGuard.tsx":
/*!******************************************************!*\
  !*** ./src/components/TierEnforcement/TierGuard.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TierGuard: () => (/* binding */ TierGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe */ \"(app-pages-browser)/./src/lib/stripe.ts\");\n/* harmony import */ var _UpgradePrompt__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UpgradePrompt */ \"(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ TierGuard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * TierGuard component that conditionally renders content based on user's subscription tier\n * Shows upgrade prompt if user doesn't have access to the feature\n */ function TierGuard(param) {\n    let { feature, children, fallback, showUpgradePrompt = true, customMessage } = param;\n    _s();\n    const { subscriptionStatus, loading } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\TierGuard.tsx\",\n            lineNumber: 31,\n            columnNumber: 12\n        }, this);\n    }\n    const userTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n    const hasAccess = (0,_lib_stripe__WEBPACK_IMPORTED_MODULE_3__.hasFeatureAccess)(userTier, feature);\n    if (hasAccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    if (fallback) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    if (showUpgradePrompt) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UpgradePrompt__WEBPACK_IMPORTED_MODULE_4__.UpgradePrompt, {\n            feature: feature,\n            currentTier: userTier,\n            customMessage: customMessage\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\TierGuard.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(TierGuard, \"pNLyuVLtm5kqKyXgAauRVqtJUQ4=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription\n    ];\n});\n_c = TierGuard;\nvar _c;\n$RefreshReg$(_c, \"TierGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RpZXJFbmZvcmNlbWVudC9UaWVyR3VhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDZ0M7QUFDdUI7QUFDakM7QUFDWTtBQVU1RDs7O0NBR0MsR0FDTSxTQUFTSyxVQUFVLEtBTVQ7UUFOUyxFQUN4QkMsT0FBTyxFQUNQQyxRQUFRLEVBQ1JDLFFBQVEsRUFDUkMsb0JBQW9CLElBQUksRUFDeEJDLGFBQWEsRUFDRSxHQU5TOztJQU94QixNQUFNLEVBQUVDLGtCQUFrQixFQUFFQyxPQUFPLEVBQUUsR0FBR1gsdUVBQWVBO0lBRXZELElBQUlXLFNBQVM7UUFDWCxxQkFBTyw4REFBQ1IscUVBQWNBOzs7OztJQUN4QjtJQUVBLE1BQU1TLFdBQVdGLENBQUFBLCtCQUFBQSx5Q0FBQUEsbUJBQW9CRyxJQUFJLEtBQUk7SUFDN0MsTUFBTUMsWUFBWWIsNkRBQWdCQSxDQUFDVyxVQUE4QlA7SUFFakUsSUFBSVMsV0FBVztRQUNiLHFCQUFPO3NCQUFHUjs7SUFDWjtJQUVBLElBQUlDLFVBQVU7UUFDWixxQkFBTztzQkFBR0E7O0lBQ1o7SUFFQSxJQUFJQyxtQkFBbUI7UUFDckIscUJBQ0UsOERBQUNOLHlEQUFhQTtZQUNaRyxTQUFTQTtZQUNUVSxhQUFhSDtZQUNiSCxlQUFlQTs7Ozs7O0lBR3JCO0lBRUEsT0FBTztBQUNUO0dBbkNnQkw7O1FBTzBCSixtRUFBZUE7OztLQVB6Q0kiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxUaWVyRW5mb3JjZW1lbnRcXFRpZXJHdWFyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU3Vic2NyaXB0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VTdWJzY3JpcHRpb24nO1xuaW1wb3J0IHsgaGFzRmVhdHVyZUFjY2VzcywgZ2V0VGllckNvbmZpZywgU3Vic2NyaXB0aW9uVGllciB9IGZyb20gJ0AvbGliL3N0cmlwZSc7XG5pbXBvcnQgeyBVcGdyYWRlUHJvbXB0IH0gZnJvbSAnLi9VcGdyYWRlUHJvbXB0JztcbmltcG9ydCBMb2FkaW5nU3Bpbm5lciBmcm9tICdAL2NvbXBvbmVudHMvdWkvTG9hZGluZ1NwaW5uZXInO1xuXG5pbnRlcmZhY2UgVGllckd1YXJkUHJvcHMge1xuICBmZWF0dXJlOiAnY3VzdG9tX3JvbGVzJyB8ICdrbm93bGVkZ2VfYmFzZScgfCAnYWR2YW5jZWRfcm91dGluZycgfCAncHJvbXB0X2VuZ2luZWVyaW5nJyB8ICdzZW1hbnRpY19jYWNoaW5nJztcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgZmFsbGJhY2s/OiBSZWFjdC5SZWFjdE5vZGU7XG4gIHNob3dVcGdyYWRlUHJvbXB0PzogYm9vbGVhbjtcbiAgY3VzdG9tTWVzc2FnZT86IHN0cmluZztcbn1cblxuLyoqXG4gKiBUaWVyR3VhcmQgY29tcG9uZW50IHRoYXQgY29uZGl0aW9uYWxseSByZW5kZXJzIGNvbnRlbnQgYmFzZWQgb24gdXNlcidzIHN1YnNjcmlwdGlvbiB0aWVyXG4gKiBTaG93cyB1cGdyYWRlIHByb21wdCBpZiB1c2VyIGRvZXNuJ3QgaGF2ZSBhY2Nlc3MgdG8gdGhlIGZlYXR1cmVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIFRpZXJHdWFyZCh7IFxuICBmZWF0dXJlLCBcbiAgY2hpbGRyZW4sIFxuICBmYWxsYmFjaywgXG4gIHNob3dVcGdyYWRlUHJvbXB0ID0gdHJ1ZSxcbiAgY3VzdG9tTWVzc2FnZSBcbn06IFRpZXJHdWFyZFByb3BzKSB7XG4gIGNvbnN0IHsgc3Vic2NyaXB0aW9uU3RhdHVzLCBsb2FkaW5nIH0gPSB1c2VTdWJzY3JpcHRpb24oKTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiA8TG9hZGluZ1NwaW5uZXIgLz47XG4gIH1cblxuICBjb25zdCB1c2VyVGllciA9IHN1YnNjcmlwdGlvblN0YXR1cz8udGllciB8fCAnZnJlZSc7XG4gIGNvbnN0IGhhc0FjY2VzcyA9IGhhc0ZlYXR1cmVBY2Nlc3ModXNlclRpZXIgYXMgU3Vic2NyaXB0aW9uVGllciwgZmVhdHVyZSk7XG5cbiAgaWYgKGhhc0FjY2Vzcykge1xuICAgIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG4gIH1cblxuICBpZiAoZmFsbGJhY2spIHtcbiAgICByZXR1cm4gPD57ZmFsbGJhY2t9PC8+O1xuICB9XG5cbiAgaWYgKHNob3dVcGdyYWRlUHJvbXB0KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxVcGdyYWRlUHJvbXB0IFxuICAgICAgICBmZWF0dXJlPXtmZWF0dXJlfVxuICAgICAgICBjdXJyZW50VGllcj17dXNlclRpZXIgYXMgU3Vic2NyaXB0aW9uVGllcn1cbiAgICAgICAgY3VzdG9tTWVzc2FnZT17Y3VzdG9tTWVzc2FnZX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiBudWxsO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3Vic2NyaXB0aW9uIiwiaGFzRmVhdHVyZUFjY2VzcyIsIlVwZ3JhZGVQcm9tcHQiLCJMb2FkaW5nU3Bpbm5lciIsIlRpZXJHdWFyZCIsImZlYXR1cmUiLCJjaGlsZHJlbiIsImZhbGxiYWNrIiwic2hvd1VwZ3JhZGVQcm9tcHQiLCJjdXN0b21NZXNzYWdlIiwic3Vic2NyaXB0aW9uU3RhdHVzIiwibG9hZGluZyIsInVzZXJUaWVyIiwidGllciIsImhhc0FjY2VzcyIsImN1cnJlbnRUaWVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/TierGuard.tsx\n"));

/***/ })

});
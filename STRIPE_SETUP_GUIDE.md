# Stripe Setup Guide for New RouKey Pricing Tiers

## 🎯 Overview

You need to create new products and prices in Stripe to match your updated pricing structure. Here's exactly what to create:

## 📋 Products to Create in Stripe Dashboard

### 1. RouKey Starter Plan
- **Product Name:** Rou<PERSON>ey Starter
- **Description:** Perfect for individual developers and small projects
- **Price:** $19.00 USD / month (recurring)
- **Billing Period:** Monthly

### 2. RouKey Professional Plan  
- **Product Name:** RouKey Professional
- **Description:** Ideal for growing businesses and development teams
- **Price:** $49.00 USD / month (recurring)
- **Billing Period:** Monthly

### 3. RouKey Enterprise Plan
- **Product Name:** RouKey Enterprise
- **Description:** For large organizations with high-volume needs
- **Price:** $149.00 USD / month (recurring)
- **Billing Period:** Monthly

## 🔧 Step-by-Step Stripe Setup

### Step 1: Create Products
1. Go to Stripe Dashboard → Products
2. Click "Add product"
3. Fill in the details for each plan above
4. Make sure to set them as "Recurring" with "Monthly" billing
5. Save each product

### Step 2: Get the IDs
After creating each product, you'll get:
- **Product ID** (starts with `prod_`)
- **Price ID** (starts with `price_`)

### Step 3: Update Your Environment Variables
Replace these in your `.env.local` file:

```env
# OLD VALUES - REPLACE THESE
STRIPE_STARTER_PRICE_ID=price_1RaA5xC97XFBBUvdt12n1i0T
STRIPE_PROFESSIONAL_PRICE_ID=price_1RaABVC97XFBBUvdkZZc1oQB
STRIPE_ENTERPRISE_PRICE_ID=price_1RaADDC97XFBBUvd7j6OPJj7

STRIPE_STARTER_PRODUCT_ID=prod_SVAQTGKo3TGhn8
STRIPE_PROFESSIONAL_PRODUCT_ID=prod_SVAWjju9MWvXvV
STRIPE_ENTERPRISE_PRODUCT_ID=prod_SVAYqDBNFsird7

# NEW VALUES - UPDATE WITH YOUR NEW IDs
STRIPE_STARTER_PRICE_ID=price_YOUR_NEW_STARTER_PRICE_ID
STRIPE_PROFESSIONAL_PRICE_ID=price_YOUR_NEW_PROFESSIONAL_PRICE_ID
STRIPE_ENTERPRISE_PRICE_ID=price_YOUR_NEW_ENTERPRISE_PRICE_ID

STRIPE_STARTER_PRODUCT_ID=prod_YOUR_NEW_STARTER_PRODUCT_ID
STRIPE_PROFESSIONAL_PRODUCT_ID=prod_YOUR_NEW_PROFESSIONAL_PRODUCT_ID
STRIPE_ENTERPRISE_PRODUCT_ID=prod_YOUR_NEW_ENTERPRISE_PRODUCT_ID

# Also update the public versions
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID=price_YOUR_NEW_STARTER_PRICE_ID
NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID=price_YOUR_NEW_PROFESSIONAL_PRICE_ID
NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID=price_YOUR_NEW_ENTERPRISE_PRICE_ID
```

## 🗄️ Database Migration

Run the database migration to add free tier support and workflow tables:

```bash
# In your rokey-app directory
npx supabase db push
```

Or manually run the SQL file in Supabase dashboard:
- Go to Supabase Dashboard → SQL Editor
- Copy and paste the contents of `supabase/migrations/20250122_add_free_tier_and_workflows.sql`
- Run the migration

## 🧪 Testing the New Setup

### Test Free Tier Signup
1. Go to `/auth/signup?plan=free`
2. Create a new account
3. Verify user gets free tier limits

### Test Paid Tier Checkout
1. Go to `/pricing`
2. Click "Get Started" on Starter/Professional plans
3. Verify Stripe checkout works with new prices

### Test Subscription Status
1. Check that existing users maintain their current tiers
2. Verify new users default to free tier
3. Test tier limit enforcement

## 📊 New Pricing Structure Summary

| Feature | Free | Starter | Professional | Enterprise |
|---------|------|---------|--------------|------------|
| **Price** | $0 | $19/mo | $49/mo | $149/mo |
| **Configurations** | 1 | 4 | 20 | Unlimited |
| **API Keys per Config** | 3 | 5 | 15 | Unlimited |
| **Custom Roles** | None | Up to 3 | Unlimited | Unlimited |
| **Workflows** | 3 | 10 | 50 | Unlimited |
| **Workflow Executions** | 100/mo | 1,000/mo | 10,000/mo | Unlimited |
| **AI Workflow Nodes** | ❌ | ✅ | ✅ | ✅ |
| **Knowledge Base** | ❌ | ❌ | ✅ (5 docs) | ✅ (Unlimited) |
| **Team Members** | 1 | 1 | 5 | Unlimited |

## 🚨 Important Notes

1. **Free Tier Users:** Don't require Stripe checkout - they signup directly
2. **Existing Users:** Will maintain their current subscription status
3. **Workflow Features:** Are new and will be built in phases
4. **Migration:** Existing users without subscriptions will be set to free tier

## 🔄 After Stripe Setup

Once you've updated the environment variables:

1. **Restart your development server**
2. **Test the pricing page** - should show 4 tiers now
3. **Test free signup** - should work without Stripe
4. **Test paid signups** - should use new Stripe prices
5. **Verify existing subscriptions** - should continue working

## 📞 Support

If you encounter any issues:
1. Check Stripe webhook logs for errors
2. Verify environment variables are correct
3. Test in Stripe's test mode first
4. Check Supabase logs for database errors

The new pricing structure positions RouKey competitively while adding significant value with workflow automation features!

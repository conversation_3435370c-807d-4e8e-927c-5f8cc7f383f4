import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get configId from query parameters
    const { searchParams } = new URL(request.url);
    const configId = searchParams.get('configId');

    if (!configId) {
      return NextResponse.json({ 
        error: 'configId is required' 
      }, { status: 400 });
    }

    // Fetch documents for this configuration
    const { data: documents, error } = await supabase
      .from('documents')
      .select(`
        id,
        filename,
        file_type,
        file_size,
        status,
        metadata,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .eq('custom_api_config_id', configId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('[Document List] Error fetching documents:', error);
      return NextResponse.json({
        error: 'Failed to fetch documents',
        details: error.message
      }, { status: 500 });
    }

    // Get chunk counts for each document
    const documentsWithStats = await Promise.all(
      documents.map(async (doc) => {
        const { count } = await supabase
          .from('document_chunks')
          .select('*', { count: 'exact', head: true })
          .eq('document_id', doc.id);

        return {
          ...doc,
          chunks_count: count || 0
        };
      })
    );

    return NextResponse.json({
      success: true,
      documents: documentsWithStats
    });

  } catch (error: any) {
    console.error('[Document List] Error:', error);
    return NextResponse.json({
      error: 'Failed to list documents',
      details: error.message
    }, { status: 500 });
  }
}

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ExclamationTriangleIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { SubscriptionTier } from '@/lib/stripe';

interface LimitIndicatorProps {
  current: number;
  limit: number;
  label: string;
  tier: SubscriptionTier;
  showUpgradeHint?: boolean;
  className?: string;
}

export function LimitIndicator({ 
  current, 
  limit, 
  label, 
  tier,
  showUpgradeHint = true,
  className = '' 
}: LimitIndicatorProps) {
  const isUnlimited = limit >= 999999;
  const percentage = isUnlimited ? 0 : (current / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = current >= limit && !isUnlimited;

  const getStatusColor = () => {
    if (isUnlimited) return 'text-green-600';
    if (isAtLimit) return 'text-red-600';
    if (isNearLimit) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusIcon = () => {
    if (isUnlimited) return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
    if (isAtLimit) return <XCircleIcon className="w-5 h-5 text-red-600" />;
    if (isNearLimit) return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />;
    return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
  };

  const getProgressBarColor = () => {
    if (isUnlimited) return 'bg-green-500';
    if (isAtLimit) return 'bg-red-500';
    if (isNearLimit) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className="text-sm font-medium text-gray-900">{label}</span>
        </div>
        <span className={`text-sm font-semibold ${getStatusColor()}`}>
          {isUnlimited ? 'Unlimited' : `${current} / ${limit}`}
        </span>
      </div>

      {!isUnlimited && (
        <div className="mb-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className={`h-2 rounded-full ${getProgressBarColor()}`}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(percentage, 100)}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </div>
      )}

      {isAtLimit && showUpgradeHint && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
          You've reached your {label.toLowerCase()} limit. 
          <button className="ml-1 underline hover:no-underline">
            Upgrade your plan
          </button> to increase your limits.
        </div>
      )}

      {isNearLimit && !isAtLimit && showUpgradeHint && (
        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
          You're approaching your {label.toLowerCase()} limit. Consider upgrading soon.
        </div>
      )}
    </div>
  );
}

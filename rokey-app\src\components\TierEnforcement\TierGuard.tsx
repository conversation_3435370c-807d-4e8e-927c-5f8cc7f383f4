'use client';

import React from 'react';
import { useSubscription } from '@/hooks/useSubscription';
import { hasFeatureAccess, getTierConfig, SubscriptionTier } from '@/lib/stripe';
import { UpgradePrompt } from './UpgradePrompt';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface TierGuardProps {
  feature: 'custom_roles' | 'knowledge_base' | 'advanced_routing' | 'prompt_engineering' | 'semantic_caching';
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  customMessage?: string;
}

/**
 * TierGuard component that conditionally renders content based on user's subscription tier
 * Shows upgrade prompt if user doesn't have access to the feature
 */
export function TierGuard({ 
  feature, 
  children, 
  fallback, 
  showUpgradePrompt = true,
  customMessage 
}: TierGuardProps) {
  const { subscriptionStatus, loading } = useSubscription();

  if (loading) {
    return <LoadingSpinner />;
  }

  const userTier = subscriptionStatus?.tier || 'free';
  const hasAccess = hasFeatureAccess(userTier as SubscriptionTier, feature);

  if (hasAccess) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    return (
      <UpgradePrompt 
        feature={feature}
        currentTier={userTier as SubscriptionTier}
        customMessage={customMessage}
      />
    );
  }

  return null;
}

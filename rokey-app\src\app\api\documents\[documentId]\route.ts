import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ documentId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId } = await params;

    if (!documentId) {
      return NextResponse.json({ 
        error: 'Document ID is required' 
      }, { status: 400 });
    }

    // First, get the document to ensure it exists and belongs to the user
    const { data: document, error: fetchError } = await supabase
      .from('documents')
      .select('id, custom_api_config_id')
      .eq('id', documentId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !document) {
      return NextResponse.json({ 
        error: 'Document not found or access denied' 
      }, { status: 404 });
    }

    // Delete document chunks first (due to foreign key constraint)
    const { error: chunksError } = await supabase
      .from('document_chunks')
      .delete()
      .eq('document_id', documentId)
      .eq('user_id', user.id);

    if (chunksError) {
      console.error('[Document Delete] Error deleting chunks:', chunksError);
      return NextResponse.json({
        error: 'Failed to delete document chunks',
        details: chunksError.message
      }, { status: 500 });
    }

    // Delete the document
    const { error: docError } = await supabase
      .from('documents')
      .delete()
      .eq('id', documentId)
      .eq('user_id', user.id);

    if (docError) {
      console.error('[Document Delete] Error deleting document:', docError);
      return NextResponse.json({
        error: 'Failed to delete document',
        details: docError.message
      }, { status: 500 });
    }

    // Invalidate training cache to ensure immediate effect
    try {
      const { trainingDataCache } = await import('@/lib/cache/trainingCache');
      trainingDataCache.invalidate(document.custom_api_config_id);
      console.log(`[Document Delete] Cache invalidated for config: ${document.custom_api_config_id}`);
    } catch (error) {
      console.warn('[Document Delete] Cache invalidation failed:', error);
    }

    console.log(`[Document Delete] Successfully deleted document: ${documentId}`);

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    });

  } catch (error: any) {
    console.error('[Document Delete] Error:', error);
    return NextResponse.json({
      error: 'Failed to delete document',
      details: error.message
    }, { status: 500 });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ documentId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId } = await params;

    if (!documentId) {
      return NextResponse.json({
        error: 'Document ID is required'
      }, { status: 400 });
    }

    // Fetch document details
    const { data: document, error } = await supabase
      .from('documents')
      .select(`
        id,
        filename,
        file_type,
        file_size,
        status,
        metadata,
        created_at,
        updated_at
      `)
      .eq('id', documentId)
      .eq('user_id', user.id)
      .single();

    if (error || !document) {
      return NextResponse.json({ 
        error: 'Document not found or access denied' 
      }, { status: 404 });
    }

    // Get chunk count
    const { count } = await supabase
      .from('document_chunks')
      .select('*', { count: 'exact', head: true })
      .eq('document_id', documentId);

    return NextResponse.json({
      success: true,
      document: {
        ...document,
        chunks_count: count || 0
      }
    });

  } catch (error: any) {
    console.error('[Document Get] Error:', error);
    return NextResponse.json({
      error: 'Failed to fetch document',
      details: error.message
    }, { status: 500 });
  }
}
